{"Version": 1, "WorkspaceRootPath": "E:\\Projects\\abozyad\\OrderFlowCore\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.core\\entities\\user.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|solutionrelative:orderflowcore.core\\entities\\user.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\supervisorservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\supervisorservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\assistantmanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\assistantmanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\assistantmanagerorderservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\assistantmanagerorderservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\accountmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\accountmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.infrastructure\\repositories\\orderrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|solutionrelative:orderflowcore.infrastructure\\repositories\\orderrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\supervisororderservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\supervisororderservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\supervisororderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\supervisororderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "User.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\User.cs", "RelativeDocumentMoniker": "OrderFlowCore.Core\\Entities\\User.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\User.cs", "RelativeToolTip": "OrderFlowCore.Core\\Entities\\User.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAxwBQAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:40:14.961Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AssistantManagerController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "ViewState": "AgIAABsAAAAAAAAAAAAgwCIAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:30:08.673Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AssistantManagerOrderService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\AssistantManagerOrderService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\AssistantManagerOrderService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\AssistantManagerOrderService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\AssistantManagerOrderService.cs", "ViewState": "AgIAAHkAAAAAAAAAAAAuwMIAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:29:22.014Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "AccountManagementService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\AccountManagementService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\AccountManagementService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\AccountManagementService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\AccountManagementService.cs", "ViewState": "AgIAALkAAAAAAAAAAAAuwLUAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:28:13.253Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "SupervisorService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\SupervisorService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorService.cs*", "RelativeToolTip": "OrderFlowCore.Application\\Services\\SupervisorService.cs*", "ViewState": "AgIAALoAAAAAAAAAAAAqwNwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:13:28.284Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "OrderRepository.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs", "RelativeDocumentMoniker": "OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs*", "RelativeToolTip": "OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs*", "ViewState": "AgIAAGEAAAAAAAAAAAAuwHEAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:08:08.797Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "SupervisorOrderService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAowCEAAABOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:07:39.577Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "SupervisorOrdersController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAQwCEAAABVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T13:58:32.253Z", "EditorCaption": ""}]}]}]}