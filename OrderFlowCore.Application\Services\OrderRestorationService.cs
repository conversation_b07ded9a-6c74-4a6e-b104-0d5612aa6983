using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class OrderRestorationService : IOrderRestorationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OrderRestorationService> _logger;

        public OrderRestorationService(
            IUnitOfWork unitOfWork,
            ILogger<OrderRestorationService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<List<RestorableOrderDto>>> GetRestorableOrdersAsync(string searchTerm, string filter)
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetRestorableOrdersAsync(searchTerm, filter);
                if (orders == null || orders.Count == 0)
                {
                    return ServiceResult<List<RestorableOrderDto>>.Failure("لا توجد طلبات قابلة للاستعادة");
                }

                return ServiceResult<List<RestorableOrderDto>>.Success(orders, $"تم العثور علي {orders.Count} طلبات قابلة للاستعادة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting restorable orders");
                return ServiceResult<List<RestorableOrderDto>>.Failure("حدث خطأ أثناء جلب الطلبات القابلة للاستعادة");
            }
        }

        public async Task<ServiceResult<RestoreDetailsDto>> GetRestoreOrderDetailsAsync(int orderId)
        {
            try
            {
                var details = await _unitOfWork.Orders.GetRestoreOrderDetailsAsync(orderId);
                if (details == null)
                {
                    return ServiceResult<RestoreDetailsDto>.Failure("لم يتم العثور على تفاصيل الطلب");
                }

                return ServiceResult<RestoreDetailsDto>.Success(details);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting restore order details for {OrderId}", orderId);
                return ServiceResult<RestoreDetailsDto>.Failure("حدث خطأ أثناء جلب تفاصيل الطلب");
            }
        }

        public async Task<ServiceResult> RestoreOrderFromSupervisorsAsync(int orderId, string restoreNotes, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Reset order to coordinator status
                order.OrderStatus = OrderStatus.B;
                order.ConfirmedByCoordinator = OrderHelper.RestoredBy(userName);
                order.CoordinatorDetails = (order.CoordinatorDetails ?? "") + $" | استعادة: {restoreNotes}";

                // Clear supervisor statuses
                await _unitOfWork.Orders.ClearSupervisorStatusesAsync(order);

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم استعادة الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring order {OrderId}", orderId);
                return ServiceResult.Failure("حدث خطأ أثناء استعادة الطلب");
            }
        }
    }
}
