namespace OrderFlowCore.Core.Entities
{
    public enum UserRole
    {
        <PERSON><PERSON><PERSON><PERSON>,
        Assistant<PERSON><PERSON><PERSON>,
        Coordinator,
        Supervisor,
        Manager
    }

    public static class UserRoleExtensions
    {
        public static string ToDisplayName(this UserRole role)
        {
            return role switch
            {
                UserRole.DirectManager => "مدير مباشر",
                UserRole.AssistantManager => "مساعد مدير",
                UserRole.Coordinator => "منسق",
                UserRole.Supervisor => "مشرف",
                UserRole.Manager => "مدير",
                _ => "غير محدد"
            };
        }

        public static string ToEnglishName(this UserRole role)
        {
            return role switch
            {
                UserRole.DirectManager => "Direct Manager",
                UserRole.AssistantManager => "Assistant Manager",
                UserRole.Coordinator => "Coordinator",
                UserRole.Supervisor => "Supervisor",
                UserRole.Manager => "Manager",
                _ => "Unknown"
            };
        }
    }
}
