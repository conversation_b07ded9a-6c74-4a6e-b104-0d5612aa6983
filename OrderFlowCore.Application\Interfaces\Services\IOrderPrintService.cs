using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IOrderPrintService
{
    Task<ServiceResult<List<OrderPrintListItemDto>>> GetPrintableOrdersAsync(string searchTerm, string filter);
    Task<ServiceResult<OrderPrintDetailsDto>> GetOrderPrintDetailsAsync(int orderId);
    Task<ServiceResult<byte[]>> DownloadOrderAttachmentsZipAsync(int orderId);
    Task<ServiceResult<byte[]>> GenerateOrderPdfAsync(int orderId);
}
