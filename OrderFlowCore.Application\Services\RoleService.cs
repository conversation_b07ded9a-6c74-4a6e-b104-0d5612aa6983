using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using System.Collections.Generic;
using System.Linq;

namespace OrderFlowCore.Application.Services
{
    public class RoleService : IRoleService
    {
        public List<string> GetAvailableRoleTypes(UserRole userRole)
        {
            return userRole switch
            {
                UserRole.Admin => new List<string>(), // Admin has no role types
                UserRole.DirectManager => GetDepartmentTypes(),
                UserRole.AssistantManager => GetAssistantManagerTypes(),
                UserRole.Coordinator => new List<string>(), // Coordinator has no role types
                UserRole.Supervisor => GetSupervisorTypes(),
                UserRole.Manager => new List<string>(), // Manager has no role types
                _ => new List<string>()
            };
        }

        public List<string> GetDepartmentTypes()
        {
            // These would typically come from the database
            // For now, returning some common department types
            return new List<string>
            {
                "قسم إدارة الأصول",
                "قسم المشتريات",
                "قسم الصيانة",
                "قسم المخازن",
                "قسم الموارد البشرية",
                "قسم المالية",
                "قسم تقنية المعلومات",
                "قسم الخدمات الطبية",
                "قسم التمريض",
                "قسم الخدمات الإدارية"
            };
        }

        public List<string> GetAssistantManagerTypes()
        {
            return new List<string>
            {
                "مساعد المدير للخدمات الطبية",
                "مساعد المدير لخدمات التمريض",
                "مساعد المدير للخدمات الإدارية والتشغيل",
                "مساعد المدير للموارد البشرية"
            };
        }

        public List<string> GetSupervisorTypes()
        {
            return new List<string>
            {
                "خدمات الموظفين مشرف",
                "إدارة تخطيط الموارد البشرية مشرف",
                "إدارة تقنية المعلومات مشرف",
                "مراقبة الدوام مشرف",
                "السجلات الطبية مشرف",
                "إدارة الرواتب والاستحقاقات مشرف",
                "إدارة القانونية والالتزام مشرف",
                "خدمات الموارد البشرية مشرف",
                "إدارة الإسكان مشرف",
                "قسم الملفات مشرف",
                "العيادات الخارجية مشرف",
                "التأمينات الاجتماعية مشرف",
                "وحدة مراقبة المخزون مشرف",
                "إدارة تنمية الإيرادات مشرف",
                "إدارة الأمن و السلامة مشرف",
                "الطب الاتصالي مشرف"
            };
        }

        public string GetRoleDisplayName(UserRole userRole, string? roleType)
        {
            var baseRole = userRole.ToDisplayName();
            
            if (!string.IsNullOrEmpty(roleType))
            {
                return $"{baseRole} - {roleType}";
            }
            
            return baseRole;
        }

        public bool ShouldShowRoleType(UserRole userRole)
        {
            return userRole switch
            {
                UserRole.DirectManager => true,
                UserRole.AssistantManager => true,
                UserRole.Supervisor => true,
                UserRole.Admin => false,
                UserRole.Coordinator => false,
                UserRole.Manager => false,
                _ => false
            };
        }

        public bool ValidateRoleType(UserRole userRole, string? roleType)
        {
            if (string.IsNullOrEmpty(roleType))
            {
                // Some roles might not require a specific type
                return !ShouldShowRoleType(userRole);
            }

            var availableTypes = GetAvailableRoleTypes(userRole);
            return availableTypes.Contains(roleType);
        }

        public SupervisorType? GetSupervisorTypeFromString(string supervisorTypeString)
        {
            return supervisorTypeString switch
            {
                "خدمات الموظفين مشرف" => SupervisorType.EmployeeServices,
                "إدارة تخطيط الموارد البشرية مشرف" => SupervisorType.HumanResourcesPlanning,
                "إدارة تقنية المعلومات مشرف" => SupervisorType.InformationTechnology,
                "مراقبة الدوام مشرف" => SupervisorType.AttendanceMonitoring,
                "السجلات الطبية مشرف" => SupervisorType.MedicalRecords,
                "إدارة الرواتب والاستحقاقات مشرف" => SupervisorType.PayrollAndBenefits,
                "إدارة القانونية والالتزام مشرف" => SupervisorType.LegalAndCompliance,
                "خدمات الموارد البشرية مشرف" => SupervisorType.HumanResourcesServices,
                "إدارة الإسكان مشرف" => SupervisorType.HousingManagement,
                "قسم الملفات مشرف" => SupervisorType.FilesSection,
                "العيادات الخارجية مشرف" => SupervisorType.OutpatientClinics,
                "التأمينات الاجتماعية مشرف" => SupervisorType.SocialInsurance,
                "وحدة مراقبة المخزون مشرف" => SupervisorType.InventoryMonitoring,
                "إدارة تنمية الإيرادات مشرف" => SupervisorType.RevenueManagement,
                "إدارة الأمن و السلامة مشرف" => SupervisorType.SecurityAndSafety,
                "الطب الاتصالي مشرف" => SupervisorType.Telemedicine,
                _ => null
            };
        }

        public AssistantManagerType? GetAssistantManagerTypeFromString(string assistantManagerTypeString)
        {
            return assistantManagerTypeString switch
            {
                "مساعد المدير للخدمات الطبية" => AssistantManagerType.A1,
                "مساعد المدير لخدمات التمريض" => AssistantManagerType.A2,
                "مساعد المدير للخدمات الإدارية والتشغيل" => AssistantManagerType.A3,
                "مساعد المدير للموارد البشرية" => AssistantManagerType.A4,
                _ => null
            };
        }
    }
}
