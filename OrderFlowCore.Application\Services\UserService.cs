using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using System.ComponentModel.DataAnnotations;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Application.Services
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAuthService _authService;

        public UserService(IUnitOfWork unitOfWork, IAuthService authService)
        {
            _unitOfWork = unitOfWork;
            _authService = authService;
        }

        public async Task<ServiceResult<UserProfileDto>> GetProfileAsync(int userId)
        {
            var user = await _unitOfWork.Users.GetUserByIdAsync(userId);
            if (user == null) 
                return ServiceResult<UserProfileDto>.Failure("User not found");
            
            var profile = new UserProfileDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                Phone = user.Phone,
                Role = user.Role
            };
            
            return ServiceResult<UserProfileDto>.Success(profile);
        }

        public async Task<ServiceResult> UpdateProfileAsync(UserProfileDto dto)
        {
            var user = await _unitOfWork.Users.GetUserByIdAsync(dto.Id);
            if (user == null) 
                return ServiceResult.Failure("User not found");

            // Validate email
            if (!string.IsNullOrEmpty(dto.Email))
            {
                var emailValidator = new EmailAddressAttribute();
                if (!emailValidator.IsValid(dto.Email))
                    return ServiceResult.Failure("Invalid email format");
                    
                var existingUser = await _unitOfWork.Users.GetByEmailAsync(dto.Email);
                if (existingUser != null && existingUser.Id != dto.Id)
                    return ServiceResult.Failure("Email already exists");
            }

            // Validate phone
            if (!string.IsNullOrEmpty(dto.Phone))
            {
                var cleanPhone = new string(dto.Phone.Where(char.IsDigit).ToArray());
                if (cleanPhone.Length < 10 || cleanPhone.Length > 15)
                    return ServiceResult.Failure("Invalid phone number format");
                dto.Phone = cleanPhone;
            }

            user.Email = dto.Email?.Trim();
            user.Phone = dto.Phone?.Trim();
            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();
            
            return ServiceResult.Success("Profile updated successfully");
        }

        public async Task<ServiceResult> ChangePasswordAsync(int userId, ChangePasswordDto dto)
        {
            var user = await _unitOfWork.Users.GetUserByIdAsync(userId);
            if (user == null) 
                return ServiceResult.Failure("User not found");
                
            if (!_authService.VerifyPassword(dto.CurrentPassword, user.Password))
                return ServiceResult.Failure("Current password is incorrect");
                
            if (dto.CurrentPassword == dto.NewPassword)
                return ServiceResult.Failure("New password must be different from current password");
                
            if (dto.NewPassword != dto.ConfirmPassword)
                return ServiceResult.Failure("New password and confirmation do not match");
                
            if (dto.NewPassword.Length < 8)
                return ServiceResult.Failure("Password must be at least 8 characters long");
                
            user.Password = _authService.HashPassword(dto.NewPassword);
            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();
            
            return ServiceResult.Success("Password changed successfully");
        }

        // Account Management Methods
        public async Task<ServiceResult<List<UserDto>>> GetAllUsersAsync()
        {
            try
            {
                var users = await _unitOfWork.Users.GetAllAsync();
                var userDtos = users.Select(u => new UserDto
                {
                    Id = u.Id,
                    Username = u.Username,
                    Email = u.Email,
                    Phone = u.Phone,
                    Role = u.Role
                }).ToList();
                
                return ServiceResult<List<UserDto>>.Success(userDtos);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<UserDto>>.Failure($"Error retrieving users: {ex.Message}");
            }
        }

        public async Task<ServiceResult<UserDto>> GetUserByUsernameAsync(string username)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByUsernameAsync(username);
                if (user == null)
                    return ServiceResult<UserDto>.Failure("User not found");

                var userDto = new UserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    Phone = user.Phone,
                    Role = user.Role
                };
                
                return ServiceResult<UserDto>.Success(userDto);
            }
            catch (Exception ex)
            {
                return ServiceResult<UserDto>.Failure($"Error retrieving user: {ex.Message}");
            }
        }

        public async Task<ServiceResult<UserDto>> CreateUserAsync(UserDto userDto)
        {
            try
            {
                // Check if username already exists
                var existingUser = await _unitOfWork.Users.GetByUsernameAsync(userDto.Username);
                if (existingUser != null)
                    return ServiceResult<UserDto>.Failure("Username already exists");

                // Check if email already exists (if provided)
                if (!string.IsNullOrEmpty(userDto.Email))
                {
                    var existingEmail = await _unitOfWork.Users.GetByEmailAsync(userDto.Email);
                    if (existingEmail != null)
                        return ServiceResult<UserDto>.Failure("Email already exists");
                }

                var user = new User
                {
                    Username = userDto.Username.Trim(),
                    Email = userDto.Email?.Trim(),
                    Phone = userDto.Phone?.Trim(),
                    Role = userDto.Role,
                    Password = _authService.HashPassword(userDto.Password),
                    UserRole = userDto.UserRole,
                    RoleType = userDto.RoleType
                };

                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();

                userDto.Id = user.Id;
                return ServiceResult<UserDto>.Success(userDto);
            }
            catch (Exception ex)
            {
                return ServiceResult<UserDto>.Failure($"Error creating user: {ex.Message}");
            }
        }

        public async Task<ServiceResult<UserDto>> UpdateUserAsync(UserDto userDto)
        {
            try
            {
                var user = await _unitOfWork.Users.GetUserByIdAsync(userDto.Id);
                if (user == null)
                    return ServiceResult<UserDto>.Failure("User not found");

                // Check if email already exists (if changed)
                if (!string.IsNullOrEmpty(userDto.Email) && userDto.Email != user.Email)
                {
                    var existingEmail = await _unitOfWork.Users.GetByEmailAsync(userDto.Email);
                    if (existingEmail != null)
                        return ServiceResult<UserDto>.Failure("Email already exists");
                }

                user.Email = userDto.Email?.Trim();
                user.Phone = userDto.Phone?.Trim();

                await _unitOfWork.Users.UpdateAsync(user);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult<UserDto>.Success(userDto);
            }
            catch (Exception ex)
            {
                return ServiceResult<UserDto>.Failure($"Error updating user: {ex.Message}");
            }
        }

        public async Task<ServiceResult> DeleteUserAsync(string username)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByUsernameAsync(username);
                if (user == null)
                    return ServiceResult.Failure("User not found");

                // Prevent deletion of Super Admin
                if (username == "Super Admin")
                    return ServiceResult.Failure("Cannot delete Super Admin user");

                await _unitOfWork.Users.DeleteAsync(user.Id);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("User deleted successfully");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error deleting user: {ex.Message}");
            }
        }
    }
} 