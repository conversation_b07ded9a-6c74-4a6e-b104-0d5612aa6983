using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{

    public class DirectManagerOrderService : IDirectManagerOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DirectManagerOrderService> _logger;

        public DirectManagerOrderService(
            IUnitOfWork unitOfWork,
            ILogger<DirectManagerOrderService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetPendingOrdersForDirectMangerAsync()
        {
            try
            {
                // Get orders that are pending manager approval
                var pendingOrders = await _unitOfWork.Orders.GetPendingOrdersForDirectMangerAsync();

                var orderSummaries = pendingOrders.Select(order => new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    OrderType = order.OrderType,
                    Department = order.Department,
                    CreatedAt = order.CreatedAt,
                    OrderStatus = order.OrderStatus
                }).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<OrderSummaryDto>>.Failure($"خطأ في جلب الطلبات المعلقة: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ConfirmOrderByDirectManagerAsync(int orderId, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                var department = await _unitOfWork.Departments.GetByNameAsync(order.Department);
                var assistantManagerId = department?.AssistantManagerId ?? AssistantManagerType.Unknown;

                // تحديد المسار والحالة الجديدة
                OrderStatus newStatus = assistantManagerId.ToOrderStatus();
                string statusMessage = assistantManagerId == AssistantManagerType.B ?
                    "تم تأكيد الطلب بنجاح وتحويله إلى منسق الموارد البشرية" : "تم تأكيد الطلب بنجاح وتحويله إلى مساعد المدير";

                // Update order status
                order.OrderStatus = newStatus;
                order.ConfirmedByDepartmentManager = OrderHelper.ConfirmedBy(userName);

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success(statusMessage);
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في تأكيد الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> RejectOrderByDirectManagerAsync(int orderId, string reason, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(reason))
                {
                    return ServiceResult.Failure("سبب الإلغاء مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Update order status
                order.ConfirmedByDepartmentManager = OrderHelper.RejectedBy(userName);
                order.ReasonForCancellation = reason;
                order.OrderStatus = OrderStatus.CancelledByDepartmentManager;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إلغاء الطلب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إلغاء الطلب: {ex.Message}");
            }
        }
    }
}
