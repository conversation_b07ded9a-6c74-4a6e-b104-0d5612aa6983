using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Web.Controllers;

[Authorize]
public class AccountsController : Controller
{
    private readonly IAccountManagementService _accountManagementService;
    private readonly ILogger<AccountsController> _logger;

    public AccountsController(
        IAccountManagementService accountManagementService,
        ILogger<AccountsController> logger)
    {
        _accountManagementService = accountManagementService;
        _logger = logger;
    }

    public async Task<IActionResult> Index(string editUsername = null)
    {
        var result = await _accountManagementService.GetAccountsDataAsync();
        if (!result.IsSuccess)
        {
            TempData["ErrorMessage"] = result.Message;
            return RedirectToAction("Index", "Dashboard");
        }
        var dto = result.Data;
        dto.AddModel = new UserCreateDto();
        dto.EditModel = new UserEditDto();

        return View(dto);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateUser(UserCreateDto dto)
    {
        var result = await _accountManagementService.CreateUserAsync(dto);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }
        return RedirectToAction("Index");
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateUser(UserEditDto dto)
    {
        var result = await _accountManagementService.UpdateUserAsync(dto);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }
        return RedirectToAction("Index");
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteUser(string username)
    {
        var result = await _accountManagementService.DeleteUserAsync(username);

        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }

        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SearchUsers(string searchTerm)
    {
        var result = await _accountManagementService.SearchUsersAsync(searchTerm);

        if (result.IsSuccess)
        {
            return View("Index", result.Data);
        }

        TempData["ErrorMessage"] = result.Message;
        return RedirectToAction(nameof(Index));
    }
}
