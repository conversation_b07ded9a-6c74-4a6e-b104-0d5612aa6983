using OrderFlowCore.Core.Entities;
using System.Collections.Generic;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface IRoleService
    {
        List<string> GetAvailableRoleTypes(UserRole userRole);
        List<string> GetDepartmentTypes();
        List<string> GetAssistantManagerTypes();
        List<string> GetSupervisorTypes();
        string GetRoleDisplayName(UserRole userRole, string? roleType);
        bool ValidateRoleType(UserRole userRole, string? roleType);
        SupervisorType? GetSupervisorTypeFromString(string supervisorTypeString);
        AssistantManagerType? GetAssistantManagerTypeFromString(string assistantManagerTypeString);
    }
}
