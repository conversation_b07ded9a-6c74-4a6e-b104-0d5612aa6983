{"Version": 1, "WorkspaceRootPath": "E:\\Projects\\abozyad\\OrderFlowCore\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\assistantmanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\assistantmanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\assistantmanagerorderservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\assistantmanagerorderservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\interfaces\\services\\iorderstypeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\interfaces\\services\\iorderstypeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "IOrdersTypeService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Interfaces\\Services\\IOrdersTypeService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Interfaces\\Services\\IOrdersTypeService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Interfaces\\Services\\IOrdersTypeService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Interfaces\\Services\\IOrdersTypeService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T13:56:38.651Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "AssistantManagerOrderService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\AssistantManagerOrderService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\AssistantManagerOrderService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\AssistantManagerOrderService.cs*", "RelativeToolTip": "OrderFlowCore.Application\\Services\\AssistantManagerOrderService.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T13:56:29.369Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "AssistantManagerController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T13:56:17.955Z", "EditorCaption": ""}]}]}]}