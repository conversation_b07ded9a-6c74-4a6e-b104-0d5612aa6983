using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Application.Interfaces.Repositories;

namespace OrderFlowCore.Infrastructure.Data;

public class UserRepository : IUserRepository
{
    private readonly ApplicationDbContext _context;

    public UserRepository(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<UserDto?> GetByUsernameAsync(string username)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Username == username);

        return user != null ? MapToDto(user) : null;
    }

    public async Task<UserDto?> GetByIdAsync(int id)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Id == id);

        return user != null ? MapToDto(user) : null;
    }

    public async Task<User?> GetByEmailAsync(string email)
    {
        return await _context.Users
            .FirstOrDefaultAsync(u => u.Email == email);
    }

    public async Task<User?> GetUserByIdAsync(int id)
    {
        return await _context.Users
            .FirstOrDefaultAsync(u => u.Id == id);
    }

    public async Task<IEnumerable<UserDto>> GetAllAsync()
    {
        var users = await _context.Users.ToListAsync();
        return users.Select(MapToDto);
    }

    public async Task<UserDto> CreateAsync(UserDto userDto)
    {
        var user = MapToEntity(userDto);
        _context.Users.Add(user);
        return MapToDto(user);
    }

    public async Task<UserDto> UpdateAsync(UserDto userDto)
    {
        var user = await _context.Users.FindAsync(userDto.Id);
        if (user == null)
            throw new ArgumentException("User not found");

        user.Username = userDto.Username;
        user.Password = userDto.Password;
        user.Email = userDto.Email;
        user.Role = userDto.Role;
        user.Phone = userDto.Phone;
        user.UserRole = userDto.UserRole;
        user.RoleType = userDto.RoleType;

        return MapToDto(user);
    }

    public async Task UpdateAsync(User user)
    {
        _context.Users.Update(user);
    }

    public async Task DeleteAsync(int id)
    {
        var user = await _context.Users.FindAsync(id);
        if (user != null)
        {
            _context.Users.Remove(user);
        }
    }

    public async Task DeleteAsync(User user)
    {
        _context.Users.Remove(user);
    }

    public async Task<bool> ExistsAsync(string username)
    {
        return await _context.Users.AnyAsync(u => u.Username == username);
    }



    public async Task AddAsync(User user)
    {
        await _context.Users.AddAsync(user);
    }

    private static UserDto MapToDto(User user)
    {
        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Password = user.Password,
            Email = user.Email,
            Role = user.Role,
            Phone = user.Phone,
            UserRole = user.UserRole,
            RoleType = user.RoleType
        };
    }

    private static User MapToEntity(UserDto userDto)
    {
        return new User
        {
            Id = userDto.Id,
            Username = userDto.Username,
            Password = userDto.Password,
            Email = userDto.Email,
            Role = userDto.Role,
            Phone = userDto.Phone,
            UserRole = userDto.UserRole,
            RoleType = userDto.RoleType
        };
    }
} 