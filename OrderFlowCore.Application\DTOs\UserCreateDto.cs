using System.ComponentModel.DataAnnotations;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.DTOs
{
    public class UserCreateDto
    {
        public string Username { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string Password { get; set; } = string.Empty;

        // New role system properties
        public UserRole UserRole { get; set; } = UserRole.DirectManager;
        public string? RoleType { get; set; }
    }
}