using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers
{
    public class OrderManagerController : Controller
    {
        private readonly IDirectManagerOrderService _directManagerOrderService;
        private readonly IHRCoordinatorOrderService _hrCoordinatorOrderService;
        private readonly IOrderManagementService _orderManagementService;
        private readonly ILogger<OrderManagerController> _logger;

        public OrderManagerController(IDirectManagerOrderService directManagerOrderService, IHRCoordinatorOrderService hrCoordinatorOrderService, IOrderManagementService orderManagementService, ILogger<OrderManagerController> logger)
        {
            _directManagerOrderService = directManagerOrderService;
            _hrCoordinatorOrderService = hrCoordinatorOrderService;
            _orderManagementService = orderManagementService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index(int? SelectedOrderId)
        {
            var model = new OrderManagerViewModel();
            var ordersResult = await _directManagerOrderService.GetPendingOrdersForDirectMangerAsync();
            if (ordersResult.IsSuccess)
            {
                model.Orders = ordersResult.Data.Select(o => new OrderSummaryViewModel
                {
                    OrderId = o.Id,
                    EmployeeName = o.EmployeeName,
                    DisplayText = $"طلب #{o.Id} - {o.EmployeeName}",
                    OrderDate = o.CreatedAt,
                    Status = o.OrderStatus
                }).ToList();
            }
            else
            {
                model.ErrorMessage = ordersResult.Message;
            }

            if (SelectedOrderId.HasValue)
            {
                var detailsResult = await _orderManagementService.GetOrderDetailsAsync(SelectedOrderId.Value);
                if (detailsResult.IsSuccess)
                {
                    var d = detailsResult.Data;
                    model.SelectedOrderId = d.Id;
                    model.SelectedOrderDetails = new OrderDetailsViewModel
                    {
                        Id = d.Id,
                        OrderType = d.OrderType,
                        EmployeeName = d.EmployeeName,
                        Department = d.Department,
                        JobTitle = d.JobTitle,
                        EmploymentType = d.EmploymentType,
                        Qualification = d.Qualification,
                        EmployeeNumber = d.EmployeeNumber,
                        CivilRecord = d.CivilRecord,
                        Nationality = d.Nationality,
                        MobileNumber = d.MobileNumber,
                        SupervisorNotes = d.Details,
                        ConfirmedByDepartmentManager = d.ConfirmedByDepartmentManager,
                        ConfirmedByAssistantManager = d.ConfirmedByAssistantManager,
                        ConfirmedByCoordinator = d.ConfirmedByCoordinator,
                        ReasonForCancellation = d.ReasonForCancellation,
                        CoordinatorDetails = d.CoordinatorDetails,
                        HumanResourcesManager = d.HumanResourcesManager,
                        SupervisorOfEmployeeServices = d.SupervisorOfEmployeeServices,
                        SupervisorOfHumanResourcesPlanning = d.SupervisorOfHumanResourcesPlanning,
                        SupervisorOfInformationTechnology = d.SupervisorOfInformationTechnology,
                        SupervisorOfAttendance = d.SupervisorOfAttendance,
                        SupervisorOfMedicalRecords = d.SupervisorOfMedicalRecords,
                        SupervisorOfPayrollAndBenefits = d.SupervisorOfPayrollAndBenefits,
                        SupervisorOfLegalAndCompliance = d.SupervisorOfLegalAndCompliance,
                        SupervisorOfHumanResourcesServices = d.SupervisorOfHumanResourcesServices,
                        SupervisorOfHousing = d.SupervisorOfHousing,
                        SupervisorOfFiles = d.SupervisorOfFiles,
                        SupervisorOfOutpatientClinics = d.SupervisorOfOutpatientClinics,
                        SupervisorOfSocialSecurity = d.SupervisorOfSocialSecurity,
                        SupervisorOfInventoryControl = d.SupervisorOfInventoryControl,
                        SupervisorOfRevenueDevelopment = d.SupervisorOfRevenueDevelopment,
                        SupervisorOfSecurity = d.SupervisorOfSecurity,
                        SupervisorOfMedicalConsultation = d.SupervisorOfMedicalConsultation
                    };
                }
                else
                {
                    model.ErrorMessage = detailsResult.Message;
                }
            }

            if (TempData["SuccessMessage"] != null)
                model.SuccessMessage = TempData["SuccessMessage"].ToString();
            if (TempData["ErrorMessage"] != null)
                model.ErrorMessage = TempData["ErrorMessage"].ToString();

            // Populate status options for dropdown
            model.StatusOptions = new List<SelectListItem>
            {
                new SelectListItem { Value = "جديد", Text = "جديد" },
                new SelectListItem { Value = "(DM)", Text = "مدير القسم" },
                new SelectListItem { Value = "(A1)", Text = "مساعد المدير للخدمات الطبية" },
                new SelectListItem { Value = "(A2)", Text = "مساعد المدير لخدمات التمريض" },
                new SelectListItem { Value = "(A3)", Text = "مساعد المدير للخدمات الإدارية والتشغيل" },
                new SelectListItem { Value = "(A4)", Text = "مساعد المدير للموارد البشرية" },
                new SelectListItem { Value = "(B)", Text = "المنسق" },
                new SelectListItem { Value = "مقبول", Text = "مقبول" },
                new SelectListItem { Value = "مرفوض", Text = "مرفوض" },
                new SelectListItem { Value = "تم الإلغاء", Text = "تم الإلغاء" }
            };
            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> Confirm(int orderId)
        {
            var userName = User.Identity?.Name;
            var result = await _directManagerOrderService.ConfirmOrderByDirectManagerAsync(orderId, userName);
            if (result.IsSuccess)
                TempData["SuccessMessage"] = "تم تأكيد الطلب بنجاح";
            else
                TempData["ErrorMessage"] = result.Message;
            return RedirectToAction("Index", new { SelectedOrderId = orderId });
        }

        [HttpPost]
        public async Task<IActionResult> Reject(int orderId, string reason)
        {
            if (string.IsNullOrWhiteSpace(reason))
            {
                TempData["ErrorMessage"] = "يرجى إدخال سبب الإلغاء";
                return RedirectToAction("Index", new { SelectedOrderId = orderId });
            }
            var userName = User.Identity?.Name;
            var result = await _directManagerOrderService.RejectOrderByDirectManagerAsync(orderId, reason, userName);
            if (result.IsSuccess)
                TempData["SuccessMessage"] = "تم إلغاء الطلب بنجاح";
            else
                TempData["ErrorMessage"] = result.Message;
            return RedirectToAction("Index", new { SelectedOrderId = orderId });
        }

        [HttpPost]
        public async Task<IActionResult> Return(int orderId, string reason)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(reason))
                {
                    TempData["ErrorMessage"] = "سبب الإعادة مطلوب";
                    return RedirectToAction("Index", new { SelectedOrderId = orderId });
                }

                var username = User.Identity?.Name ?? "مدير";
                var result = await _hrCoordinatorOrderService.ReturnOrderToAssistantManagerAsync(orderId, reason, username);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"خطأ في إعادة الطلب: {ex.Message}";
            }

            return RedirectToAction("Index", new { SelectedOrderId = orderId });
        }

        [HttpPost]
        public async Task<IActionResult> ChangeStatus(int orderId, string newStatus, string notes)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(newStatus))
                {
                    TempData["ErrorMessage"] = "الحالة الجديدة مطلوبة";
                    return RedirectToAction("Index", new { SelectedOrderId = orderId });
                }

                var username = User.Identity?.Name ?? "مدير";

                // For now, we'll use the reject method for status changes with notes
                // In a full implementation, you'd add a specific ChangeStatusAsync method
                var result = await _directManagerOrderService.RejectOrderByDirectManagerAsync(orderId, notes ?? "تم تغيير الحالة", username);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = "تم تغيير حالة الطلب بنجاح";
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"خطأ في تغيير حالة الطلب: {ex.Message}";
            }

            return RedirectToAction("Index", new { SelectedOrderId = orderId });
        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int orderId)
        {
            var result = await _orderManagementService.DownloadAttachmentsZipAsync(orderId);
            if (!result.IsSuccess || result.Data == null)
            {
                TempData["ErrorMessage"] = result.Message ?? "لا توجد مرفقات";
                return RedirectToAction("Index", new { SelectedOrderId = orderId });
            }
            string fileName = $"مرفقات_طلب_{orderId}.zip";
            return File(result.Data, "application/zip", fileName);
        }
    }
} 