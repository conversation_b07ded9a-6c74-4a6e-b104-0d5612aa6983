using OrderFlowCore.Core.Models;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Services;

namespace OrderFlowCore.Application.Services
{
    #region Supporting Services

    public class SupervisorService : ISupervisorService
    {
        private readonly Dictionary<string, SupervisorPropertyAccessor> _supervisorMapping;

        public SupervisorService()
        {
            _supervisorMapping = InitializeSupervisorMapping();
        }

        public List<SupervisorRejectionDto> ExtractRejections(OrdersTable order)
        {
            var rejections = new List<SupervisorRejectionDto>();
            var supervisorColumns = GetSupervisorList(order);

            foreach (var supervisor in supervisorColumns)
            {
                if (IsRejection(supervisor.Value))
                {
                    rejections.Add(CreateRejectionDto(supervisor.Key, supervisor.Value));
                }
            }

            return rejections;
        }

        public void UpdateSupervisorStatuses(OrdersTable order, List<string> selectedSupervisors, string statusWithDate)
        {
            foreach (var supervisor in selectedSupervisors)
            {
                if (_supervisorMapping.TryGetValue(supervisor, out var accessor))
                {
                    var currentValue = accessor.Getter(order);
                    if (ShouldUpdateStatus(currentValue))
                    {
                        accessor.Setter(order, statusWithDate);
                    }
                }
            }
        }

        public void ClearUnderImplementationStatuses(OrdersTable order)
        {
            var underImplementation = OrderHelper.OrderUnderImplementation();

            foreach (var accessor in _supervisorMapping.Values)
            {
                var currentValue = accessor.Getter(order);
                if (currentValue == underImplementation)
                {
                    accessor.Setter(order, null);
                }
            }
        }

        public List<string> GetAssignedSupervisors(OrdersTable order)
        {
            var supervisors = new List<string>();
            var supervisorStatuses = GetSupervisorList(order);

            foreach (var supervisor in supervisorStatuses)
            {
                if (!string.IsNullOrEmpty(supervisor.Value))
                {
                    supervisors.Add(supervisor.Key);
                }
            }

            return supervisors;
        }

        public string GetSupervisorStatus(OrdersTable order, string supervisorRole)
        {
            if (order == null || string.IsNullOrWhiteSpace(supervisorRole))
                return null;
            if (_supervisorMapping.TryGetValue(supervisorRole, out var accessor))
            {
                return accessor.Getter(order);
            }
            return null;
        }

        private Dictionary<string, string> GetSupervisorList(OrdersTable order)
        {
            var statuses = new Dictionary<string, string>();

            foreach (var kvp in _supervisorMapping)
            {
                var displayName = GetSupervisorDisplayName(kvp.Key);
                var value = kvp.Value.Getter(order);
                statuses[displayName] = value;
            }

            return statuses;
        }

        private bool IsRejection(string value)
        {
            return !string.IsNullOrEmpty(value) && value.Contains("تمت الإعادة");
        }

        private bool ShouldUpdateStatus(string currentValue)
        {
            return string.IsNullOrEmpty(currentValue) ||
                   currentValue == "/" ||
                   currentValue.Contains("تمت الإعادة");
        }

        private SupervisorRejectionDto CreateRejectionDto(string supervisorName, string rejectionDetails)
        {
            return new SupervisorRejectionDto
            {
                SupervisorName = supervisorName,
                RejectionDetails = rejectionDetails,
                RejectionReason = rejectionDetails,
                RejectionDate = DateTime.Now.ToString("yyyy-MM-dd")
            };
        }

        private string GetSupervisorDisplayName(string key)
        {
            return $"مشرف {key}";
        }

        private Dictionary<string, SupervisorPropertyAccessor> InitializeSupervisorMapping()
        {
            return new Dictionary<string, SupervisorPropertyAccessor>
            {
                ["خدمات الموظفين"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfEmployeeServices = value,
                    order => order.SupervisorOfEmployeeServices
                ),
                ["إدارة تخطيط الموارد البشرية"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfHumanResourcesPlanning = value,
                    order => order.SupervisorOfHumanResourcesPlanning
                ),
                ["إدارة تقنية المعلومات"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfInformationTechnology = value,
                    order => order.SupervisorOfInformationTechnology
                ),
                ["مراقبة الدوام"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfAttendance = value,
                    order => order.SupervisorOfAttendance
                ),
                ["السجلات الطبية"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfMedicalRecords = value,
                    order => order.SupervisorOfMedicalRecords
                ),
                ["إدارة الرواتب والاستحقاقات"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfPayrollAndBenefits = value,
                    order => order.SupervisorOfPayrollAndBenefits
                ),
                ["إدارة القانونية والالتزام"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfLegalAndCompliance = value,
                    order => order.SupervisorOfLegalAndCompliance
                ),
                ["خدمات الموارد البشرية"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfHumanResourcesServices = value,
                    order => order.SupervisorOfHumanResourcesServices
                ),
                ["إدارة الإسكان"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfHousing = value,
                    order => order.SupervisorOfHousing
                ),
                ["قسم الملفات"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfFiles = value,
                    order => order.SupervisorOfFiles
                ),
                ["العيادات الخارجية"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfOutpatientClinics = value,
                    order => order.SupervisorOfOutpatientClinics
                ),
                ["التأمينات الاجتماعية"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfSocialSecurity = value,
                    order => order.SupervisorOfSocialSecurity
                ),
                ["وحدة مراقبة المخزون"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfInventoryControl = value,
                    order => order.SupervisorOfInventoryControl
                ),
                ["إدارة تنمية الإيرادات"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfRevenueDevelopment = value,
                    order => order.SupervisorOfRevenueDevelopment
                ),
                ["إدارة الأمن و السلامة"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfSecurity = value,
                    order => order.SupervisorOfSecurity
                ),
                ["الطب الاتصالي"] = new SupervisorPropertyAccessor(
                    (order, value) => order.SupervisorOfMedicalConsultation = value,
                    order => order.SupervisorOfMedicalConsultation
                )
            };
        }

        private class SupervisorPropertyAccessor
        {
            public Action<OrdersTable, string> Setter { get; }
            public Func<OrdersTable, string> Getter { get; }

            public SupervisorPropertyAccessor(
                Action<OrdersTable, string> setter,
                Func<OrdersTable, string> getter)
            {
                Setter = setter;
                Getter = getter;
            }
        }
    }

    #endregion
}