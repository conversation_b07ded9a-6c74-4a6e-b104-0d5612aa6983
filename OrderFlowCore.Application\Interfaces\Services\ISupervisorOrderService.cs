using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface ISupervisorOrderService
{
    Task<ServiceResult<List<OrderSummaryDto>>> GetSupervisorOrdersAsync(string supervisorRole);
    Task<ServiceResult> ConfirmOrderBySupervisorAsync(int orderId, string supervisorRole, string userName);
    Task<ServiceResult> NeedsActionBySupervisorAsync(int orderId, string actionRequired, string supervisorRole, string userName);
    Task<ServiceResult> RejectOrderBySupervisorAsync(int orderId, string rejectReason, string supervisorRole, string userName);
}
