namespace OrderFlowCore.Core.Entities
{
    public enum SupervisorType
    {
        Unknown,
        EmployeeServices,                    // خدمات الموظفين مشرف
        HumanResourcesPlanning,             // إدارة تخطيط الموارد البشرية مشرف
        InformationTechnology,              // إدارة تقنية المعلومات مشرف
        AttendanceMonitoring,               // مراقبة الدوام مشرف
        MedicalRecords,                     // السجلات الطبية مشرف
        PayrollAndBenefits,                 // إدارة الرواتب والاستحقاقات مشرف
        LegalAndCompliance,                 // إدارة القانونية والالتزام مشرف
        HumanResourcesServices,             // خدمات الموارد البشرية مشرف
        HousingManagement,                  // إدارة الإسكان مشرف
        FilesSection,                       // قسم الملفات مشرف
        OutpatientClinics,                  // العيادات الخارجية مشرف
        SocialInsurance,                    // التأمينات الاجتماعية مشرف
        InventoryMonitoring,                // وحدة مراقبة المخزون مشرف
        RevenueManagement,                  // إدارة تنمية الإيرادات مشرف
        SecurityAndSafety,                  // إدارة الأمن و السلامة مشرف
        Telemedicine                        // الطب الاتصالي مشرف
    }

    public static class SupervisorTypeExtensions
    {
        public static string ToDisplayName(this SupervisorType type)
        {
            return type switch
            {
                SupervisorType.EmployeeServices => "خدمات الموظفين مشرف",
                SupervisorType.HumanResourcesPlanning => "إدارة تخطيط الموارد البشرية مشرف",
                SupervisorType.InformationTechnology => "إدارة تقنية المعلومات مشرف",
                SupervisorType.AttendanceMonitoring => "مراقبة الدوام مشرف",
                SupervisorType.MedicalRecords => "السجلات الطبية مشرف",
                SupervisorType.PayrollAndBenefits => "إدارة الرواتب والاستحقاقات مشرف",
                SupervisorType.LegalAndCompliance => "إدارة القانونية والالتزام مشرف",
                SupervisorType.HumanResourcesServices => "خدمات الموارد البشرية مشرف",
                SupervisorType.HousingManagement => "إدارة الإسكان مشرف",
                SupervisorType.FilesSection => "قسم الملفات مشرف",
                SupervisorType.OutpatientClinics => "العيادات الخارجية مشرف",
                SupervisorType.SocialInsurance => "التأمينات الاجتماعية مشرف",
                SupervisorType.InventoryMonitoring => "وحدة مراقبة المخزون مشرف",
                SupervisorType.RevenueManagement => "إدارة تنمية الإيرادات مشرف",
                SupervisorType.SecurityAndSafety => "إدارة الأمن و السلامة مشرف",
                SupervisorType.Telemedicine => "الطب الاتصالي مشرف",
                _ => "غير محدد"
            };
        }

        public static string ToEnglishName(this SupervisorType type)
        {
            return type switch
            {
                SupervisorType.EmployeeServices => "Employee Services Supervisor",
                SupervisorType.HumanResourcesPlanning => "HR Planning Management Supervisor",
                SupervisorType.InformationTechnology => "IT Management Supervisor",
                SupervisorType.AttendanceMonitoring => "Attendance Monitoring Supervisor",
                SupervisorType.MedicalRecords => "Medical Records Supervisor",
                SupervisorType.PayrollAndBenefits => "Payroll and Benefits Management Supervisor",
                SupervisorType.LegalAndCompliance => "Legal and Compliance Management Supervisor",
                SupervisorType.HumanResourcesServices => "HR Services Supervisor",
                SupervisorType.HousingManagement => "Housing Management Supervisor",
                SupervisorType.FilesSection => "Files Section Supervisor",
                SupervisorType.OutpatientClinics => "Outpatient Clinics Supervisor",
                SupervisorType.SocialInsurance => "Social Insurance Supervisor",
                SupervisorType.InventoryMonitoring => "Inventory Monitoring Unit Supervisor",
                SupervisorType.RevenueManagement => "Revenue Development Management Supervisor",
                SupervisorType.SecurityAndSafety => "Security and Safety Management Supervisor",
                SupervisorType.Telemedicine => "Telemedicine Supervisor",
                _ => "Unknown"
            };
        }
    }
}
